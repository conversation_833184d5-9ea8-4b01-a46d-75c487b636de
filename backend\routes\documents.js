const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { protect } = require('../middleware/auth');
const Document = require('../models/Document');
const Tenant = require('../models/Tenant');

// Configure multer for file upload
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '..', 'uploads', 'documents');
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        // Generate unique filename with original extension
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

const fileFilter = (req, file, cb) => {
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Invalid file type. Only PDF, JPG, PNG, DOC, and DOCX files are allowed.'), false);
    }
};

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: fileFilter
});

// Upload document
router.post('/upload', protect, upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ message: 'No file uploaded' });
        }

        const { tenantId, documentType, expiryDate } = req.body;

        // Validate tenant exists
        const tenant = await Tenant.findOne({
            _id: tenantId,
            createdBy: req.user.id
        });

        if (!tenant) {
            // Delete uploaded file if tenant not found
            fs.unlinkSync(req.file.path);
            return res.status(404).json({ message: 'Tenant not found' });
        }

        const document = await Document.create({
            tenant: tenantId,
            documentType,
            fileName: req.file.originalname,
            filePath: req.file.path,
            fileSize: req.file.size,
            mimeType: req.file.mimetype,
            expiryDate: expiryDate || null,
            createdBy: req.user.id
        });

        const docObj = document.toObject();
        docObj.id = docObj._id;
        delete docObj._id;
        delete docObj.__v;

        res.status(201).json({
            success: true,
            data: docObj
        });
    } catch (err) {
        console.error(err);
        // Delete uploaded file if document creation fails
        if (req.file) {
            fs.unlinkSync(req.file.path);
        }
        res.status(500).json({ message: 'Error uploading document' });
    }
});

// Get all documents
router.get('/', protect, async (req, res) => {
    try {
        const documents = await Document.find({ createdBy: req.user.id })
            .populate('tenant', 'fullName')
            .sort({ uploadDate: -1 });

        const transformedDocs = documents.map(doc => {
            const docObj = doc.toObject();
            docObj.id = docObj._id;
            delete docObj._id;
            delete docObj.__v;
            return docObj;
        });

        res.json({
            success: true,
            data: transformedDocs
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Get documents by tenant
router.get('/tenant/:tenantId', protect, async (req, res) => {
    try {
        const documents = await Document.find({
            tenant: req.params.tenantId,
            createdBy: req.user.id
        }).sort({ uploadDate: -1 });

        const transformedDocs = documents.map(doc => {
            const docObj = doc.toObject();
            docObj.id = docObj._id;
            delete docObj._id;
            return docObj;
        });

        res.json({
            success: true,
            data: transformedDocs
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Download document
router.get('/download/:id', protect, async (req, res) => {
    try {
        const document = await Document.findOne({
            _id: req.params.id,
            createdBy: req.user.id
        });

        if (!document) {
            return res.status(404).json({ message: 'Document not found' });
        }

        if (!fs.existsSync(document.filePath)) {
            return res.status(404).json({ message: 'File not found' });
        }

        res.download(document.filePath, document.fileName);
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Error downloading document' });
    }
});

// Delete document
router.delete('/:id', protect, async (req, res) => {
    try {
        const document = await Document.findOne({
            _id: req.params.id,
            createdBy: req.user.id
        });

        if (!document) {
            return res.status(404).json({ message: 'Document not found' });
        }

        // Delete file from storage
        if (fs.existsSync(document.filePath)) {
            fs.unlinkSync(document.filePath);
        }

        await document.remove();

        res.json({
            success: true,
            data: {}
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

module.exports = router; 