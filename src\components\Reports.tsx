
import React, { useState } from 'react';
import { Download, Filter, Calendar, BarChart3, DollarSign } from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';

export const Reports: React.FC = () => {
  const [dateRange, setDateRange] = useState('monthly');
  const [selectedBuilding, setSelectedBuilding] = useState('all');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  const buildings = ['All Buildings', 'Ashirward Empro Park 5', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];

  const reportSummary = {
    totalRentCollected: 245000,
    totalGSTCollected: 44100,
    pendingRent: 45000,
    pendingGST: 8100,
    totalDeposits: 1250000,
    totalTenants: 47,
  };

  const handleExport = (format: string) => {
    console.log(`Exporting report in ${format} format`);
    alert(`Report exported in ${format.toUpperCase()} format!`);
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-6">Report Filters</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <Label htmlFor="dateRange">Report Period</Label>
            <select
              id="dateRange"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="monthly">This Month</option>
              <option value="quarterly">This Quarter</option>
              <option value="yearly">This Year</option>
              <option value="custom">Custom Range</option>
            </select>
          </div>

          <div>
            <Label htmlFor="building">Building</Label>
            <select
              id="building"
              value={selectedBuilding}
              onChange={(e) => setSelectedBuilding(e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            >
              {buildings.map(building => (
                <option key={building} value={building.toLowerCase().replace(' ', '-')}>{building}</option>
              ))}
            </select>
          </div>

          {dateRange === 'custom' && (
            <>
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </>
          )}
        </div>

        <div className="flex space-x-4 mt-6">
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Filter className="h-4 w-4 mr-2" />
            Apply Filters
          </Button>
          <Button onClick={() => handleExport('pdf')} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button onClick={() => handleExport('excel')} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
        </div>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <BarChart3 className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-xs font-medium text-muted-foreground">Rent Collected</p>
              <p className="text-lg font-bold">₹{reportSummary.totalRentCollected.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <DollarSign className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-xs font-medium text-muted-foreground">GST Collected</p>
              <p className="text-lg font-bold">₹{reportSummary.totalGSTCollected.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <Calendar className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <p className="text-xs font-medium text-muted-foreground">Pending Rent</p>
              <p className="text-lg font-bold">₹{reportSummary.pendingRent.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div>
            <p className="text-xs font-medium text-muted-foreground">Pending GST</p>
            <p className="text-lg font-bold">₹{reportSummary.pendingGST.toLocaleString()}</p>
          </div>
        </Card>

        <Card className="p-4">
          <div>
            <p className="text-xs font-medium text-muted-foreground">Total Deposits</p>
            <p className="text-lg font-bold">₹{reportSummary.totalDeposits.toLocaleString()}</p>
          </div>
        </Card>

        <Card className="p-4">
          <div>
            <p className="text-xs font-medium text-muted-foreground">Total Tenants</p>
            <p className="text-lg font-bold">{reportSummary.totalTenants}</p>
          </div>
        </Card>
      </div>

      {/* Detailed Reports */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Monthly Collection Summary</h3>
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Ashirward Empro Park 5</h4>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Rent:</span>
                <span className="font-medium">₹70,000</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">GST:</span>
                <span className="font-medium">₹12,600</span>
              </div>
              <div className="flex justify-between text-sm font-semibold border-t pt-1">
                <span>Total:</span>
                <span>₹82,600</span>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-sm">Plot 5 Ashirward Empro</h4>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Rent:</span>
                <span className="font-medium">₹85,000</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">GST:</span>
                <span className="font-medium">₹15,300</span>
              </div>
              <div className="flex justify-between text-sm font-semibold border-t pt-1">
                <span>Total:</span>
                <span>₹1,00,300</span>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-sm">Plot 6 Ashirward Empro</h4>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Rent:</span>
                <span className="font-medium">₹90,000</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">GST:</span>
                <span className="font-medium">₹16,200</span>
              </div>
              <div className="flex justify-between text-sm font-semibold border-t pt-1">
                <span>Total:</span>
                <span>₹1,06,200</span>
              </div>
            </div>

            <hr className="my-4" />
            <div className="space-y-2 bg-green-50 p-3 rounded-lg">
              <div className="flex justify-between font-semibold">
                <span>Total Rent Collected:</span>
                <span>₹2,45,000</span>
              </div>
              <div className="flex justify-between font-semibold">
                <span>Total GST Collected:</span>
                <span>₹44,100</span>
              </div>
              <div className="flex justify-between font-bold text-green-700 border-t pt-2">
                <span>Grand Total:</span>
                <span>₹2,89,100</span>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Pending Collections</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center border-b pb-3">
              <div>
                <p className="font-medium">Amit Patel</p>
                <p className="text-sm text-muted-foreground">Ashirward Empro Park 5 - 3rd Floor</p>
              </div>
              <div className="text-right">
                <p className="text-red-600 font-medium">₹18,500</p>
                <p className="text-xs text-muted-foreground">+ ₹3,330 GST</p>
              </div>
            </div>

            <div className="flex justify-between items-center border-b pb-3">
              <div>
                <p className="font-medium">Rita Joshi</p>
                <p className="text-sm text-muted-foreground">Plot 5 Ashirward Empro - 2nd Floor</p>
              </div>
              <div className="text-right">
                <p className="text-red-600 font-medium">₹22,000</p>
                <p className="text-xs text-muted-foreground">+ ₹3,960 GST</p>
              </div>
            </div>

            <div className="flex justify-between items-center border-b pb-3">
              <div>
                <p className="font-medium">Suresh Kumar</p>
                <p className="text-sm text-muted-foreground">Plot 6 Ashirward Empro - GF</p>
              </div>
              <div className="text-right">
                <p className="text-red-600 font-medium">₹4,500</p>
                <p className="text-xs text-muted-foreground">+ ₹810 GST</p>
              </div>
            </div>

            <div className="bg-red-50 p-3 rounded-lg mt-4">
              <div className="flex justify-between font-semibold text-red-700">
                <span>Total Pending Rent:</span>
                <span>₹45,000</span>
              </div>
              <div className="flex justify-between font-semibold text-red-700">
                <span>Total Pending GST:</span>
                <span>₹8,100</span>
              </div>
              <div className="flex justify-between font-bold text-red-800 border-t border-red-200 pt-2 mt-2">
                <span>Total Pending:</span>
                <span>₹53,100</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};
