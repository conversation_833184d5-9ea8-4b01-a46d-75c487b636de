const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const Tenant = require('../models/Tenant');
const { protect } = require('../middleware/auth');

// Create tenant
router.post('/', protect, [
    body('fullName').notEmpty().withMessage('Full name is required'),
    body('phone').notEmpty().withMessage('Phone number is required'),
    body('address').notEmpty().withMessage('Address is required'),
    body('building').notEmpty().withMessage('Building is required'),
    body('floor').notEmpty().withMessage('Floor is required'),
    body('rentDetails.cashRent').isNumeric().withMessage('Cash rent must be a number'),
    body('rentDetails.bankRent').isNumeric().withMessage('Bank rent must be a number'),
    body('rentDetails.roomRent').isNumeric().withMessage('Room rent must be a number'),
    body('deposit').isNumeric().withMessage('Deposit must be a number'),
    body('rentStartDate').notEmpty().withMessage('Rent start date is required'),
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const {
            fullName,
            firmName,
            phone,
            email,
            address,
            building,
            floor,
            rentDetails,
            deposit,
            rentStartDate,
            nextIncrementDate,
            notes
        } = req.body;

        // Calculate GST (18% on bank rent)
        const gstAmount = Math.round(rentDetails.bankRent * 0.18);

        // Calculate total rent (cash rent + bank rent + room rent)
        const totalRent = rentDetails.cashRent + rentDetails.bankRent + rentDetails.roomRent;

        // Calculate total payable (total rent + GST)
        const totalPayable = totalRent + gstAmount;

        const tenant = await Tenant.create({
            fullName,
            firmName,
            phone,
            email,
            address,
            building,
            floor,
            rentDetails: {
                ...rentDetails,
                gstAmount,
                totalRent,
                totalPayable
            },
            deposit,
            rentStartDate,
            nextIncrementDate,
            notes,
            createdBy: req.user.id
        });

        res.status(201).json({
            success: true,
            data: tenant
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Get all tenants
router.get('/', protect, async (req, res) => {
    try {
        const tenants = await Tenant.find({ createdBy: req.user.id })
            .sort({ createdAt: -1 });

        // Transform _id to id in the response
        const transformedTenants = tenants.map(tenant => {
            const tenantObj = tenant.toObject();
            tenantObj.id = tenantObj._id;
            delete tenantObj._id;
            return tenantObj;
        });

        res.json({
            success: true,
            data: transformedTenants
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Get single tenant
router.get('/:id', protect, async (req, res) => {
    try {
        const tenant = await Tenant.findOne({
            _id: req.params.id,
            createdBy: req.user.id
        });

        if (!tenant) {
            return res.status(404).json({ message: 'Tenant not found' });
        }

        // Transform _id to id in the response
        const tenantObj = tenant.toObject();
        tenantObj.id = tenantObj._id;
        delete tenantObj._id;

        res.json({
            success: true,
            data: tenantObj
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Update tenant
router.put('/:id', protect, async (req, res) => {
    try {
        const {
            fullName,
            firmName,
            phone,
            email,
            address,
            building,
            floor,
            rentDetails,
            deposit,
            rentStartDate,
            nextIncrementDate,
            notes,
            isActive
        } = req.body;

        // Calculate GST (18% on bank rent)
        const gstAmount = Math.round(rentDetails.bankRent * 0.18);

        // Calculate total rent (cash rent + bank rent + room rent)
        const totalRent = rentDetails.cashRent + rentDetails.bankRent + rentDetails.roomRent;

        // Calculate total payable (total rent + GST)
        const totalPayable = totalRent + gstAmount;

        let tenant = await Tenant.findOne({
            _id: req.params.id,
            createdBy: req.user.id
        });

        if (!tenant) {
            return res.status(404).json({ message: 'Tenant not found' });
        }

        tenant = await Tenant.findByIdAndUpdate(
            req.params.id,
            {
                fullName,
                firmName,
                phone,
                email,
                address,
                building,
                floor,
                rentDetails: {
                    ...rentDetails,
                    gstAmount,
                    totalRent,
                    totalPayable
                },
                deposit,
                rentStartDate,
                nextIncrementDate,
                notes,
                isActive
            },
            { new: true }
        );

        // Transform _id to id in the response
        const tenantObj = tenant.toObject();
        tenantObj.id = tenantObj._id;
        delete tenantObj._id;

        res.json({
            success: true,
            data: tenantObj
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Delete tenant
router.delete('/:id', protect, async (req, res) => {
    try {
        const tenant = await Tenant.findOne({
            _id: req.params.id,
            createdBy: req.user.id
        });

        if (!tenant) {
            return res.status(404).json({ message: 'Tenant not found' });
        }

        await tenant.remove();

        res.json({
            success: true,
            data: {}
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

module.exports = router; 