
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card } from './ui/card';
import { Calculator, Save, User, Building } from 'lucide-react';

// Mock tenant data - in real app, this would come from your database
const mockTenantData = {
  'Ashirward Empro Park 5-GF': {
    fullName: '<PERSON><PERSON>',
    firmName: 'Kumar Enterprises',
    phone: '+91 9876543210',
    email: '<EMAIL>',
    cashRent: 15000,
    roomRent: 2000,
    deposit: 45000,
    rentStartDate: '2024-01-01'
  },
  'Ashirward Empro Park 5-1st': {
    fullName: 'P<PERSON>',
    firmName: 'Sharma Trading Co.',
    phone: '+91 9876543211',
    email: '<EMAIL>',
    cashRent: 18000,
    roomRent: 2500,
    deposit: 55000,
    rentStartDate: '2024-02-01'
  },
  'Plot 5 Ashirward Empro-GF': {
    fullName: 'Am<PERSON>',
    firmName: 'Patel Industries',
    phone: '+91 9876543212',
    email: '<EMAIL>',
    cashRent: 12000,
    roomRent: 1800,
    deposit: 38000,
    rentStartDate: '2024-01-15'
  }
};

export const RentEntryForm: React.FC = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    firmName: '',
    phone: '',
    email: '',
    building: '',
    floor: 'GF',
    cashRent: 0,
    gstRent: 0,
    roomRent: 0,
    totalRent: 0,
    deposit: 0,
    rentStartDate: '',
    rentMonth: new Date().toISOString().slice(0, 7), // Current month YYYY-MM
    isExistingTenant: false
  });

  const buildings = ['Ashirward Empro Park 5', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const floors = ['GF', '1st', '2nd', '3rd', '4th', '5th'];

  const calculateGST = (cashRent: number) => {
    return Math.round(cashRent * 0.18);
  };

  const handleBuildingFloorChange = (building: string, floor: string) => {
    const key = `${building}-${floor}`;
    const tenantData = mockTenantData[key as keyof typeof mockTenantData];

    if (tenantData) {
      const gstRent = calculateGST(tenantData.cashRent);
      setFormData(prev => ({
        ...prev,
        building,
        floor,
        fullName: tenantData.fullName,
        firmName: tenantData.firmName,
        phone: tenantData.phone,
        email: tenantData.email,
        cashRent: tenantData.cashRent,
        gstRent: gstRent,
        roomRent: tenantData.roomRent,
        totalRent: tenantData.cashRent + gstRent + tenantData.roomRent,
        deposit: tenantData.deposit,
        rentStartDate: tenantData.rentStartDate,
        isExistingTenant: true
      }));
    } else {
      // New tenant
      setFormData(prev => ({
        ...prev,
        building,
        floor,
        fullName: '',
        firmName: '',
        phone: '',
        email: '',
        cashRent: 0,
        gstRent: 0,
        roomRent: 0,
        totalRent: 0,
        deposit: 0,
        rentStartDate: '',
        isExistingTenant: false
      }));
    }
  };

  const handleCashRentChange = (value: number) => {
    const gstRent = calculateGST(value);
    setFormData(prev => ({
      ...prev,
      cashRent: value,
      gstRent: gstRent,
      totalRent: value + gstRent + prev.roomRent
    }));
  };

  const handleRoomRentChange = (value: number) => {
    setFormData(prev => ({
      ...prev,
      roomRent: value,
      totalRent: prev.cashRent + prev.gstRent + value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Rent entry saved:', formData);
    // Here you would save to your backend
    alert(`Rent for ${formData.rentMonth} saved successfully!`);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Monthly Rent Entry</h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Month Selection */}
              <div>
                <Label htmlFor="rentMonth">Rent Month *</Label>
                <Input
                  id="rentMonth"
                  type="month"
                  value={formData.rentMonth}
                  onChange={(e) => setFormData(prev => ({ ...prev, rentMonth: e.target.value }))}
                  required
                />
              </div>

              {/* Property Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="building">Building *</Label>
                  <select
                    id="building"
                    value={formData.building}
                    onChange={(e) => handleBuildingFloorChange(e.target.value, formData.floor)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select Building</option>
                    {buildings.map(building => (
                      <option key={building} value={building}>{building}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="floor">Floor *</Label>
                  <select
                    id="floor"
                    value={formData.floor}
                    onChange={(e) => handleBuildingFloorChange(formData.building, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    {floors.map(floor => (
                      <option key={floor} value={floor}>{floor}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Auto-populated Tenant Information */}
              {formData.isExistingTenant && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <User className="h-5 w-5 text-green-600" />
                    <h4 className="font-medium text-green-800">Tenant Details (Auto-populated)</h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Name:</span>
                      <span className="ml-2 font-medium">{formData.fullName}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Firm:</span>
                      <span className="ml-2 font-medium">{formData.firmName}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Phone:</span>
                      <span className="ml-2 font-medium">{formData.phone}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Email:</span>
                      <span className="ml-2 font-medium">{formData.email}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* New Tenant Form */}
              {!formData.isExistingTenant && formData.building && (
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">New Tenant Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="fullName">Full Name *</Label>
                      <Input
                        id="fullName"
                        value={formData.fullName}
                        onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                        placeholder="Enter tenant's full name"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="firmName">Firm Name</Label>
                      <Input
                        id="firmName"
                        value={formData.firmName}
                        onChange={(e) => setFormData(prev => ({ ...prev, firmName: e.target.value }))}
                        placeholder="Enter firm/company name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number *</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                        placeholder="Enter phone number"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="Enter email address"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Rent Amount (Editable) */}
              {formData.building && (
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Rent Details - {formData.rentMonth}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="cashRent">Cash Rent (₹) *</Label>
                      <Input
                        id="cashRent"
                        type="number"
                        value={formData.cashRent || ''}
                        onChange={(e) => handleCashRentChange(Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium"
                      />
                    </div>
                    <div>
                      <Label htmlFor="gstRent">GST Rent (18%) (₹)</Label>
                      <Input
                        id="gstRent"
                        type="number"
                        value={formData.gstRent || ''}
                        readOnly
                        className="bg-gray-50"
                      />
                    </div>
                    <div>
                      <Label htmlFor="roomRent">Room Rent (₹)</Label>
                      <Input
                        id="roomRent"
                        type="number"
                        value={formData.roomRent || ''}
                        onChange={(e) => handleRoomRentChange(Number(e.target.value))}
                        placeholder="0"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Deposit (One-time for new tenants) */}
              {!formData.isExistingTenant && formData.building && (
                <div>
                  <Label htmlFor="deposit">Deposit Amount (₹) * (One-time)</Label>
                  <Input
                    id="deposit"
                    type="number"
                    value={formData.deposit || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, deposit: Number(e.target.value) }))}
                    placeholder="0"
                    required
                    className="text-lg font-medium"
                  />
                </div>
              )}

              {formData.building && (
                <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3">
                  <Save className="h-5 w-5 mr-2" />
                  Save Rent Entry for {formData.rentMonth}
                </Button>
              )}
            </form>
          </Card>
        </div>

        {/* Preview Card */}
        <div className="lg:col-span-1">
          <Card className="p-6 sticky top-8">
            <div className="flex items-center space-x-2 mb-4">
              <Calculator className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">Rent Summary</h3>
            </div>

            {formData.building ? (
              <div className="space-y-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-2 text-blue-700 mb-2">
                    <Building className="h-4 w-4" />
                    <span className="font-medium">Property</span>
                  </div>
                  <p className="text-sm">{formData.building} - {formData.floor}</p>
                  <p className="text-sm text-gray-600">Month: {formData.rentMonth}</p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Cash Rent:</span>
                    <span className="font-medium">₹{formData.cashRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">GST (18%):</span>
                    <span className="font-medium">₹{formData.gstRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Room Rent:</span>
                    <span className="font-medium">₹{formData.roomRent.toLocaleString()}</span>
                  </div>
                  <hr className="my-3" />
                  <div className="flex justify-between text-lg">
                    <span className="font-semibold text-gray-900">Total Rent:</span>
                    <span className="font-bold text-blue-600">₹{formData.totalRent.toLocaleString()}</span>
                  </div>

                  {formData.deposit > 0 && (
                    <div className="flex justify-between border-t pt-3">
                      <span className="text-sm text-gray-600">
                        {formData.isExistingTenant ? 'Deposit (On File):' : 'New Deposit:'}
                      </span>
                      <span className="font-medium">₹{formData.deposit.toLocaleString()}</span>
                    </div>
                  )}
                </div>

                {formData.fullName && (
                  <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">
                      {formData.isExistingTenant ? 'Existing Tenant' : 'New Tenant'}
                    </h4>
                    <p className="text-sm text-gray-600">Name: {formData.fullName}</p>
                    {formData.firmName && (
                      <p className="text-sm text-gray-600">Firm: {formData.firmName}</p>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">
                Select building and floor to view rent details
              </p>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};
