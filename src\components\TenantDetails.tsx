import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { ArrowLeft, Edit, FileText, Download } from 'lucide-react';
import { getTenant, Tenant } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

interface TenantDetailsProps {
  tenantId: string;
  onBack: () => void;
  onEdit: () => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'unpaid':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'increment-due':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'paid':
      return 'Paid';
    case 'unpaid':
      return 'Unpaid';
    case 'increment-due':
      return 'Increment Due';
    default:
      return status;
  }
};

export const TenantDetails: React.FC<TenantDetailsProps> = ({ tenantId, onBack, onEdit }) => {
  const { toast } = useToast();
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTenant();
  }, [tenantId]);

  const loadTenant = async () => {
    try {
      setIsLoading(true);
      const response = await getTenant(tenantId);
      setTenant(response.data);
    } catch (error: any) {
      console.error('Error loading tenant details:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load tenant details. Please check if the server is running.",
        variant: "destructive",
      });
      // Navigate back to tenants list on error
      onBack();
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-500 dark:text-gray-400">Loading tenant details...</p>
      </div>
    );
  }

  if (!tenant) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <p className="text-gray-500 dark:text-gray-400 mb-4">Tenant not found</p>
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Tenants
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Tenants
          </Button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{tenant.fullName}</h1>
        </div>
        <Button onClick={onEdit} className="bg-blue-600 hover:bg-blue-700">
          <Edit className="h-4 w-4 mr-2" />
          Edit Tenant
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Personal Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Personal Information</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Full Name</label>
              <p className="text-gray-900 dark:text-white">{tenant.fullName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Firm/Company</label>
              <p className="text-gray-900 dark:text-white">{tenant.firmName || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
              <p className="text-gray-900 dark:text-white">{tenant.phone}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
              <p className="text-gray-900 dark:text-white">{tenant.email || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Address</label>
              <p className="text-gray-900 dark:text-white">{tenant.address}</p>
            </div>
          </div>
        </Card>

        {/* Property & Rent Details */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Property & Rent Details</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Property</label>
              <p className="text-gray-900 dark:text-white">{tenant.building} - {tenant.floor}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Cash Rent</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.rentDetails.cashRent.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Bank Rent</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.rentDetails.bankRent.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Room Rent</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.rentDetails.roomRent.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">GST (18%)</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.rentDetails.gstAmount.toLocaleString()}</p>
            </div>
            <div className="border-t pt-3">
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Rent</label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">₹{tenant.rentDetails.totalRent.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Payable (Inc. GST)</label>
              <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">₹{tenant.rentDetails.totalPayable.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Deposit</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.deposit.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        {/* Additional Info */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Additional Information</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Rent Start Date</label>
              <p className="text-gray-900 dark:text-white">{format(new Date(tenant.rentStartDate), 'PPP')}</p>
            </div>
            {tenant.nextIncrementDate && (
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Next Increment Date</label>
                <p className="text-gray-900 dark:text-white">{format(new Date(tenant.nextIncrementDate), 'PPP')}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(tenant.status || '')}`}>
                {getStatusText(tenant.status || '')}
              </span>
            </div>
            {tenant.notes && (
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</label>
                <p className="text-gray-900 dark:text-white text-sm whitespace-pre-wrap">{tenant.notes}</p>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Documents */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Documents</h3>
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 flex items-center justify-center text-gray-500 dark:text-gray-400">
            <p>No documents uploaded yet</p>
          </div>
        </div>
      </Card>
    </div>
  );
};
